# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdSemanticsLabelsAPI": {
                        "alias": {
                            "UsdSchemaBase": "SemanticsLabelsAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "SemanticsLabelsAPI", 
                        "schemaKind": "multipleApplyAPI"
                    }
                }
            }, 
            "LibraryPath": "../../../../../../../../Binaries/Win64/usd_usdSemantics.dll", 
            "Name": "usdSemantics", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}

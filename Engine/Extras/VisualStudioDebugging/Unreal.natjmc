<?xml version="1.0" encoding="utf-8"?>
<NonUserCode xmlns="http://schemas.microsoft.com/vstudio/debugger/jmc/2013">
  <!-- This file should be placed or symlink'd into %USERPROFILE%\Documents\Visual Studio 20xx\Visualizers -->

  <!-- Skip stepping into placement operator new and atomics - these should be in .natstepfilter but it doesn't seem to work -->
  <File Name="*atomic" />
  <Function Name="operator new*" />
  <Function Name="std::_Atomic*" />

  <!-- Skip stepping into platform atomics -->
  <File Name="*\Engine\Source\Runtime\Core\Public\Windows\WindowsPlatformTLS.h" />

  <!-- Skip stepping into invocations -->
  <File Name="*\Engine\Source\Runtime\Core\Public\Templates\Invoke.h" />
  <File Name="*\Engine\Source\Runtime\Core\Public\Templates\Tuple.h" />

  <!-- Skip stepping into containers and allocators -->
  <File Name="*\Engine\Source\Runtime\Core\Public\Containers\Array.h" />
  <File Name="*\Engine\Source\Runtime\Core\Public\Containers\BitArray.h" />
  <File Name="*\Engine\Source\Runtime\Core\Public\Containers\Deque.h" />
  <File Name="*\Engine\Source\Runtime\Core\Public\Containers\Map.h" />
  <File Name="*\Engine\Source\Runtime\Core\Public\Containers\PagedArray.h" />
  <File Name="*\Engine\Source\Runtime\Core\Public\Containers\Set.h" />
  <File Name="*\Engine\Source\Runtime\Core\Public\Containers\SortedMap.h" />
  <File Name="*\Engine\Source\Runtime\Core\Public\Containers\SparseArray.h" />
  <File Name="*\Engine\Source\Runtime\Core\Public\Containers\ContainerAllocationPolicies.h" />
  <File Name="*\Engine\Source\Runtime\Core\Public\Misc\Optional.h" />

  <!-- Skip stepping into TFunction, TFunctionRef and TUniqueFunction -->
  <File Name="*\Engine\Source\Runtime\Core\Public\Templates\Function.h" />

  <!-- Skip stepping into delegate invocations -->
  <File Name="*\Engine\Source\Runtime\Core\Public\Delegates\*" />
  <File Name="*\Engine\Source\Runtime\Core\Private\Delegates\*" />
  <File Name="*\Engine\Source\Runtime\Core\Public\Misc\MTAccessDetector.h" />
  <File Name="*\Engine\Source\Runtime\Core\Private\Misc\MTAccessDetector.cpp" />
</NonUserCode>
